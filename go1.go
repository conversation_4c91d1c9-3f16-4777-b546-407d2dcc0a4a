package main

import (
	"fmt"
	"time"
)

func main() {
	// Current time
	now := time.Now()

	fmt.Printf("Current time: %v\n", now)
	fmt.Printf("Current time (formatted): %s\n\n", now.Format("2000-01-02 15:04:05"))

	// ตัวอย่าง Format Patterns ต่างๆ
	fmt.Println("=== Format Pattern Examples ===")
	fmt.Printf("ISO 8601: %s\n", now.Format("2006-01-02T15:04:05Z07:00"))
	fmt.Printf("US Format: %s\n", now.Format("01/02/2006 3:04 PM"))
	fmt.Printf("Thai Style: %s\n", now.Format("2/1/2006 15:04:05"))
	fmt.Printf("Long Format: %s\n", now.Format("Monday, January 2, 2006"))
	fmt.Printf("Short Date: %s\n", now.Format("Jan 2, 06"))
	fmt.Printf("Time Only 24h: %s\n", now.Format("15:04:05"))
	fmt.Printf("Time Only 12h: %s\n", now.Format("3:04:05 PM"))
	fmt.Printf("Year only: %s\n", now.Format("2006"))
	fmt.Printf("Month only: %s\n", now.Format("January"))
	fmt.Printf("Day only: %s\n", now.Format("Monday"))
	fmt.Printf("Custom: %s\n", now.Format("วันที่ 2 เดือน January ปี 2006 เวลา 15:04 น."))
	fmt.Println()

	// Different time formats
	fmt.Println("=== Different Time Formats ===")
	fmt.Printf("RFC3339: %s\n", now.Format(time.RFC3339))
	fmt.Printf("Kitchen: %s\n", now.Format(time.Kitchen))
	fmt.Println("Hello, World!" + "555")

}
