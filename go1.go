package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("=== Go Date and Time Examples ===\n")

	// Current time
	now := time.Now()
	fmt.Printf("Current time: %v\n", now)
	fmt.Printf("Current time (formatted): %s\n\n", now.Format("2006-01-02 15:04:05"))

	// Different time formats
	fmt.Println("=== Different Time Formats ===")
	fmt.Printf("RFC3339: %s\n", now.Format(time.RFC3339))
	fmt.Printf("Kitchen: %s\n", now.Format(time.Kitchen))
	fmt.Printf("Stamp: %s\n", now.Format(time.Stamp))
	fmt.Printf("Custom: %s\n", now.Format("Monday, January 2, 2006 at 3:04 PM"))
	fmt.Printf("Date only: %s\n", now.Format("2006-01-02"))
	fmt.Printf("Time only: %s\n\n", now.Format("15:04:05"))

	// Time components
	fmt.Println("=== Time Components ===")
	fmt.Printf("Year: %d\n", now.Year())
	fmt.Printf("Month: %s (%d)\n", now.Month(), int(now.Month()))
	fmt.Printf("Day: %d\n", now.Day())
	fmt.Printf("Hour: %d\n", now.Hour())
	fmt.Printf("Minute: %d\n", now.Minute())
	fmt.Printf("Second: %d\n", now.Second())
	fmt.Printf("Weekday: %s\n", now.Weekday())
	fmt.Printf("Day of year: %d\n\n", now.YearDay())

	// Creating specific dates
	fmt.Println("=== Creating Specific Dates ===")
	specificDate := time.Date(2024, time.December, 25, 10, 30, 0, 0, time.UTC)
	fmt.Printf("Christmas 2024: %s\n", specificDate.Format("Monday, January 2, 2006 at 3:04 PM MST"))

	// Parsing dates from strings
	dateStr := "2024-06-15 14:30:45"
	parsed, err := time.Parse("2006-01-02 15:04:05", dateStr)
	if err != nil {
		fmt.Printf("Error parsing date: %v\n", err)
	} else {
		fmt.Printf("Parsed date: %s\n\n", parsed.Format("January 2, 2006 at 3:04 PM"))
	}

	// Time calculations
	fmt.Println("=== Time Calculations ===")
	tomorrow := now.AddDate(0, 0, 1)
	fmt.Printf("Tomorrow: %s\n", tomorrow.Format("2006-01-02 15:04:05"))

	nextWeek := now.Add(7 * 24 * time.Hour)
	fmt.Printf("Next week: %s\n", nextWeek.Format("2006-01-02 15:04:05"))

	oneHourAgo := now.Add(-1 * time.Hour)
	fmt.Printf("One hour ago: %s\n", oneHourAgo.Format("2006-01-02 15:04:05"))

	// Duration between times
	duration := tomorrow.Sub(now)
	fmt.Printf("Duration until tomorrow: %v\n", duration)
	fmt.Printf("Hours until tomorrow: %.2f\n\n", duration.Hours())

	// Unix timestamp
	fmt.Println("=== Unix Timestamps ===")
	fmt.Printf("Unix timestamp: %d\n", now.Unix())
	fmt.Printf("Unix nano: %d\n", now.UnixNano())

	// Convert from Unix timestamp
	unixTime := time.Unix(1640995200, 0) // Jan 1, 2022
	fmt.Printf("From Unix timestamp: %s\n\n", unixTime.Format("2006-01-02 15:04:05"))

	// Time zones
	fmt.Println("=== Time Zones ===")
	utc := now.UTC()
	fmt.Printf("UTC: %s\n", utc.Format("2006-01-02 15:04:05 MST"))

	// Load different time zones
	if loc, err := time.LoadLocation("America/New_York"); err == nil {
		nyTime := now.In(loc)
		fmt.Printf("New York: %s\n", nyTime.Format("2006-01-02 15:04:05 MST"))
	}

	if loc, err := time.LoadLocation("Asia/Tokyo"); err == nil {
		tokyoTime := now.In(loc)
		fmt.Printf("Tokyo: %s\n", tokyoTime.Format("2006-01-02 15:04:05 MST"))
	}
}
