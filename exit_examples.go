package main

import (
	"fmt"
	"os"
)

// ตัวอย่าง function ที่ใช้ return
func checkAge(age int) string {
	if age < 0 {
		return "Invalid age"  // ออกจาก function ทันที
	}
	if age < 18 {
		return "Minor"
	}
	return "Adult"
}

// ตัวอย่าง function ที่อาจใช้ panic
func divide(a, b float64) float64 {
	if b == 0 {
		panic("Cannot divide by zero!")  // หยุดโปรแกรมเมื่อเกิดข้อผิดพลาด
	}
	return a / b
}

// ตัวอย่าง function ที่มี early return
func processNumber(num int) {
	fmt.Printf("Processing number: %d\n", num)
	
	if num < 0 {
		fmt.Println("Negative number, exiting function")
		return  // ออกจาก function เร็ว
	}
	
	if num == 0 {
		fmt.Println("Zero detected, exiting function")
		return  // ออกจาก function เร็ว
	}
	
	fmt.Printf("Result: %d squared is %d\n", num, num*num)
}

func main() {
	fmt.Println("=== Exit Commands Examples ===\n")

	// 1. ตัวอย่าง return ใน function
	fmt.Println("1. Return examples:")
	fmt.Println("Age 15:", checkAge(15))
	fmt.Println("Age 25:", checkAge(25))
	fmt.Println("Age -5:", checkAge(-5))
	fmt.Println()

	// 2. ตัวอย่าง early return
	fmt.Println("2. Early return examples:")
	processNumber(5)
	processNumber(-3)
	processNumber(0)
	processNumber(7)
	fmt.Println()

	// 3. ตัวอย่าง break และ continue ใน loop
	fmt.Println("3. Break example (หยุดเมื่อเจอ 5):")
	for i := 1; i <= 10; i++ {
		if i == 5 {
			fmt.Printf("Breaking at %d\n", i)
			break  // ออกจาก loop
		}
		fmt.Printf("%d ", i)
	}
	fmt.Println("\n")

	fmt.Println("4. Continue example (ข้ามเลขคู่):")
	for i := 1; i <= 10; i++ {
		if i%2 == 0 {
			continue  // ข้ามไปรอบถัดไป
		}
		fmt.Printf("%d ", i)  // แสดงเฉพาะเลขคี่
	}
	fmt.Println("\n")

	// 5. ตัวอย่าง panic (ระวัง: จะหยุดโปรแกรม)
	fmt.Println("5. Division examples:")
	fmt.Printf("10 / 2 = %.2f\n", divide(10, 2))
	fmt.Printf("15 / 3 = %.2f\n", divide(15, 3))
	
	// Uncomment บรรทัดนี้เพื่อดู panic
	// fmt.Printf("10 / 0 = %.2f\n", divide(10, 0))  // จะทำให้โปรแกรมหยุด
	
	fmt.Println("\n6. Program will exit normally here")
	
	// ตัวอย่าง os.Exit() - uncomment เพื่อทดสอบ
	// fmt.Println("About to exit with os.Exit()")
	// os.Exit(0)  // จบโปรแกรมทันที
	// fmt.Println("This line will never execute")
	
	fmt.Println("Program completed successfully!")
}
